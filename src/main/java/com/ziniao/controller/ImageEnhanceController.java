package com.ziniao.controller;

import com.ziniao.model.ImageEnhanceRequest;
import com.ziniao.model.ImageEnhanceResponse;
import com.ziniao.service.ImageEnhanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * AI图像增强控制器
 */
@Api(tags = "AI图像增强接口")
@RestController
@RequestMapping("/api/image-enhance")
@CrossOrigin(origins = "*")
public class ImageEnhanceController {
    
    private static final Logger logger = LoggerFactory.getLogger(ImageEnhanceController.class);
    
    @Autowired
    private ImageEnhanceService imageEnhanceService;
    
    /**
     * AI图像增强处理
     */
    @ApiOperation(value = "AI图像增强处理", notes = "对图片进行质量增强、风格转换或色彩增强")
    @PostMapping("/process")
    public ResponseEntity<ImageEnhanceResponse> processImageEnhance(
            @ApiParam(value = "图像增强请求参数", required = true) @Valid @RequestBody ImageEnhanceRequest request) {

        try {
            logger.info("收到AI图像增强请求: {}", request);

            ImageEnhanceResponse response = imageEnhanceService.processImageEnhance(request);

            if (response.isSuccess()) {
                logger.info("AI图像增强处理成功");
                return ResponseEntity.ok(response);
            } else {
                logger.error("AI图像增强处理失败: {}", response.getMsg());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("AI图像增强处理异常", e);
            
            ImageEnhanceResponse errorResponse = new ImageEnhanceResponse();
            errorResponse.setCode("500");
            errorResponse.setMsg("系统异常: " + e.getMessage());
            errorResponse.setSuccess(false);
            
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
    
    /**
     * 查询AI图像增强任务结果
     */
    @ApiOperation(value = "查询AI图像增强结果", notes = "根据任务ID查询AI图像增强的处理结果和状态")
    @GetMapping("/result/{taskId}")
    public ResponseEntity<ImageEnhanceResponse> queryTaskResult(
            @ApiParam(value = "任务ID", required = true, example = "1945877291232894976")
            @PathVariable String taskId) {

        try {
            logger.info("查询图像增强任务结果, taskId: {}", taskId);

            ImageEnhanceResponse response = imageEnhanceService.queryEnhanceResult(taskId);

            if (response.isSuccess()) {
                logger.info("图像增强任务结果查询成功");
                return ResponseEntity.ok(response);
            } else {
                logger.error("图像增强任务结果查询失败: {}", response.getMsg());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            logger.error("查询图像增强任务结果异常", e);

            ImageEnhanceResponse errorResponse = new ImageEnhanceResponse();
            errorResponse.setCode("500");
            errorResponse.setMsg("查询异常: " + e.getMessage());
            errorResponse.setSuccess(false);

            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 健康检查
     */
    @ApiOperation(value = "图像增强服务健康检查", notes = "检查图像增强服务是否正常运行")
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        logger.info("图像增强服务健康检查");
        return ResponseEntity.ok("图像增强服务运行正常");
    }
    
    /**
     * 获取支持的增强类型
     */
    @ApiOperation(value = "获取支持的增强类型", notes = "获取系统支持的所有图像增强类型")
    @GetMapping("/enhance-types")
    public ResponseEntity<String[]> getEnhanceTypes() {
        logger.info("获取支持的增强类型");
        String[] types = {"quality", "style", "color"};
        return ResponseEntity.ok(types);
    }
    
    /**
     * 获取支持的输出格式
     */
    @ApiOperation(value = "获取支持的输出格式", notes = "获取系统支持的所有输出格式")
    @GetMapping("/output-formats")
    public ResponseEntity<String[]> getOutputFormats() {
        logger.info("获取支持的输出格式");
        String[] formats = {"jpg", "png", "webp"};
        return ResponseEntity.ok(formats);
    }
}
