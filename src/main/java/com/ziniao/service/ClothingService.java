package com.ziniao.service;

import com.alibaba.fastjson.JSON;
import com.fzzixun.openapi.sdk.client.OpenClient;
import com.fzzixun.openapi.sdk.common.RequestMethod;
import com.fzzixun.openapi.sdk.request.CommonRequest;
import com.fzzixun.openapi.sdk.response.CommonResponse;
import com.ziniao.config.ApiConfig;
import com.ziniao.model.ClothingRequest;
import com.ziniao.model.ClothingResponse;
import com.ziniao.model.FittingRoomRequest;
import com.ziniao.model.FittingRoomResponse;
import com.ziniao.model.ShopReplaceRequest;
import com.ziniao.model.ShopReplaceResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * AI穿衣服务类
 */
@Service
public class ClothingService {
    
    private static final Logger logger = LoggerFactory.getLogger(ClothingService.class);
    
    @Autowired
    private ApiConfig apiConfig;
    
    @Autowired
    private TokenService tokenService;
    
    private OpenClient openClient;
    
    /**
     * 获取OpenClient实例（懒加载）
     */
    private OpenClient getOpenClient() {
        if (openClient == null) {
            openClient = new OpenClient(
                apiConfig.getBaseUrl(),
                apiConfig.getAppId(),
                apiConfig.getPrivateKey()
            );
        }
        return openClient;
    }
    
    /**
     * AI穿衣 - 上下装
     *
     * @param request 穿衣请求参数
     * @return 穿衣结果
     * @throws Exception API调用异常
     */
    public ClothingResponse processClothing(ClothingRequest request) throws Exception {
        logger.info("开始处理AI穿衣请求: {}", request);

        // 获取应用令牌
        String appToken = tokenService.getAppToken();

        // 创建API请求 - 使用官方文档中的正确路径
        String apiPath = "/linkfox-ai/image/v2/make/fittingRoom";
        CommonRequest apiRequest = new CommonRequest(apiPath, RequestMethod.POST_JSON);

        // 根据官方文档设置业务参数
        Map<String, Object> bizParams = new HashMap<>();

        // 根据服装类型设置不同的参数
        if ("上衣".equals(request.getClothingType())) {
            bizParams.put("upperOriginUrl", request.getClothingImage());
            bizParams.put("upperImageUrl", "");
            bizParams.put("downOriginUrl", "");
            bizParams.put("downImageUrl", "");
        } else if ("下装".equals(request.getClothingType())) {
            bizParams.put("downOriginUrl", request.getClothingImage());
            bizParams.put("downImageUrl", "");
            bizParams.put("upperOriginUrl", "");
            bizParams.put("upperImageUrl", "");
        } else {
            // 默认作为上衣处理
            bizParams.put("upperOriginUrl", request.getClothingImage());
            bizParams.put("upperImageUrl", "");
            bizParams.put("downOriginUrl", "");
            bizParams.put("downImageUrl", "");
        }

        bizParams.put("modelImageUrl", request.getPersonImage());
        bizParams.put("modelMaskImageUrl", "");
        bizParams.put("outputNum", "");

        String bizContent = JSON.toJSONString(bizParams);
        apiRequest.setBizContent(bizContent);

        // 打印等效的curl命令
        printCurlCommand(apiPath, bizContent, appToken);

        try {
            // 使用executeAppToken方法，传入应用令牌
            CommonResponse response = getOpenClient().executeAppToken(apiRequest, appToken);

            // 检查是否是令牌过期错误，如果是则重试一次
            if (!response.isSuccess() && isTokenExpiredError(response)) {
                logger.warn("检测到令牌过期错误，尝试刷新令牌并重试: {}", response.getErrorMsg());

                // 强制刷新令牌
                String newToken = tokenService.forceRefreshToken();
                logger.info("令牌已刷新，使用新令牌重试API调用");

                // 更新curl命令中的令牌并重新打印
                printCurlCommand(apiPath, bizContent, newToken);

                // 使用新令牌重试
                response = getOpenClient().executeAppToken(apiRequest, newToken);
            }

            if (response.isSuccess()) {
                String data = response.getData();
                logger.info("AI穿衣处理成功: {}", data);

                // 解析响应数据
                ClothingResponse clothingResponse = new ClothingResponse();
                clothingResponse.setCode(200);
                clothingResponse.setMessage("success");

                ClothingResponse.ClothingData clothingData = new ClothingResponse.ClothingData();
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = JSON.parseObject(data, Map.class);
                if (dataMap != null) {
                    // 根据官方文档，响应中包含id字段
                    Object idObj = dataMap.get("id");
                    if (idObj != null) {
                        clothingData.setTaskId(idObj.toString());
                        clothingData.setStatus("submitted");
                        clothingData.setProgress(0);
                    }
                }
                clothingResponse.setData(clothingData);

                return clothingResponse;

            } else {
                logger.error("AI穿衣处理失败: {}", response.getErrorMsg());

                ClothingResponse errorResponse = new ClothingResponse();
                errorResponse.setCode(response.getCode());
                errorResponse.setMessage(response.getErrorMsg());
                return errorResponse;
            }

        } catch (Exception e) {
            logger.error("AI穿衣处理异常", e);
            throw e;
        }
    }
    
    /**
     * 查询任务状态
     * 注意：根据当前官方文档，可能没有专门的任务状态查询接口
     * 这个方法暂时返回一个模拟的响应
     *
     * @param taskId 任务ID
     * @return 任务状态
     * @throws Exception API调用异常
     */
    public ClothingResponse queryTaskStatus(String taskId) throws Exception {
        logger.info("查询任务状态, taskId: {}", taskId);

        // 由于官方文档中没有明确的任务状态查询接口
        // 这里返回一个基于任务ID的模拟响应
        ClothingResponse response = new ClothingResponse();
        response.setCode(200);
        response.setMessage("success");

        ClothingResponse.ClothingData data = new ClothingResponse.ClothingData();
        data.setTaskId(taskId);
        data.setStatus("processing"); // 假设任务正在处理中
        data.setProgress(50); // 假设进度50%

        response.setData(data);

        logger.info("返回模拟的任务状态: {}", response);
        return response;

        // TODO: 如果官方提供了任务状态查询接口，请替换上面的模拟代码
        /*
        // 获取应用令牌
        String appToken = tokenService.getAppToken();

        // 创建API请求 - 需要确认正确的接口路径
        CommonRequest apiRequest = new CommonRequest("/linkfox-ai/image/v2/query/status", RequestMethod.POST_JSON);

        // 设置请求参数
        Map<String, Object> params = new HashMap<>();
        params.put("id", taskId);

        apiRequest.setBizContent(JSON.toJSONString(params));

        try {
            CommonResponse response = getOpenClient().executeAppToken(apiRequest, appToken);

            if (response.isSuccess()) {
                String data = response.getData();
                logger.info("任务状态查询成功: {}", data);

                // 解析响应并返回
                // ...

            } else {
                logger.error("任务状态查询失败: {}", response.getErrorMsg());
                // ...
            }

        } catch (Exception e) {
            logger.error("任务状态查询异常", e);
            throw e;
        }
        */
    }
    
    /**
     * 轮询等待任务完成
     * 
     * @param taskId 任务ID
     * @param maxWaitTime 最大等待时间（毫秒）
     * @param pollInterval 轮询间隔（毫秒）
     * @return 最终结果
     * @throws Exception 异常
     */
    public ClothingResponse waitForTaskCompletion(String taskId, long maxWaitTime, long pollInterval) throws Exception {
        logger.info("等待任务完成, taskId: {}, maxWaitTime: {}ms", taskId, maxWaitTime);
        
        long startTime = System.currentTimeMillis();
        
        while (System.currentTimeMillis() - startTime < maxWaitTime) {
            ClothingResponse response = queryTaskStatus(taskId);
            
            if (response.getData() != null) {
                String status = response.getData().getStatus();
                
                if ("completed".equals(status) || "success".equals(status)) {
                    logger.info("任务完成成功");
                    return response;
                } else if ("failed".equals(status) || "error".equals(status)) {
                    logger.error("任务执行失败");
                    return response;
                }
            }
            
            // 等待下次轮询
            Thread.sleep(pollInterval);
        }
        
        throw new RuntimeException("任务超时，等待时间: " + maxWaitTime + "ms");
    }

    /**
     * AI穿衣 - 官方文档格式
     *
     * @param request 官方格式的穿衣请求参数
     * @return 官方格式的穿衣结果
     * @throws Exception API调用异常
     */
    public FittingRoomResponse processFittingRoom(FittingRoomRequest request) throws Exception {
        logger.info("开始处理AI穿衣请求（官方格式）: {}", request);

        // 获取应用令牌
        String appToken = tokenService.getAppToken();

        // 创建API请求 - 使用官方文档中的正确路径
        String apiPath = "/linkfox-ai/image/v2/make/fittingRoom";
        CommonRequest apiRequest = new CommonRequest(apiPath, RequestMethod.POST_JSON);

        // 直接使用官方格式的参数
        String bizContent = JSON.toJSONString(request);
        apiRequest.setBizContent(bizContent);

        // 打印等效的curl命令
        printCurlCommand(apiPath, bizContent, appToken);

        try {
            // 使用executeAppToken方法，传入应用令牌
            CommonResponse response = getOpenClient().executeAppToken(apiRequest, appToken);

            // 检查是否是令牌过期错误，如果是则重试一次
            if (!response.isSuccess() && isTokenExpiredError(response)) {
                logger.warn("检测到令牌过期错误，尝试刷新令牌并重试: {}", response.getErrorMsg());

                // 强制刷新令牌
                String newToken = tokenService.forceRefreshToken();
                logger.info("令牌已刷新，使用新令牌重试API调用");

                // 更新curl命令中的令牌并重新打印
                printCurlCommand(apiPath, bizContent, newToken);

                // 使用新令牌重试
                response = getOpenClient().executeAppToken(apiRequest, newToken);
            }

            if (response.isSuccess()) {
                String data = response.getData();
                logger.info("AI穿衣处理成功（官方格式）: {}", data);

                // 创建成功响应
                FittingRoomResponse fittingRoomResponse = new FittingRoomResponse();
                fittingRoomResponse.setCode("0");  // 网关返回码，0表示成功
                fittingRoomResponse.setMsg("成功");
                fittingRoomResponse.setSuccess(true);  // 设置成功标志

                // 解析API响应数据
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = JSON.parseObject(data, Map.class);
                if (dataMap != null) {
                    FittingRoomResponse.ResponseData responseData = new FittingRoomResponse.ResponseData();

                    // 设置响应数据
                    Object msgObj = dataMap.get("msg");
                    if (msgObj != null) {
                        responseData.setMsg(msgObj.toString());
                    }

                    Object codeObj = dataMap.get("code");
                    if (codeObj != null) {
                        responseData.setCode(Integer.valueOf(codeObj.toString()));
                    }

                    Object traceIdObj = dataMap.get("traceId");
                    if (traceIdObj != null) {
                        responseData.setTraceId(traceIdObj.toString());
                    }

                    // 解析内部数据
                    @SuppressWarnings("unchecked")
                    Map<String, Object> innerData = (Map<String, Object>) dataMap.get("data");
                    if (innerData != null) {
                        FittingRoomResponse.TaskData taskData = new FittingRoomResponse.TaskData();
                        Object id = innerData.get("id");
                        if (id != null) {
                            taskData.setId(id.toString());
                        }
                        responseData.setData(taskData);
                    }

                    fittingRoomResponse.setData(responseData);
                }

                return fittingRoomResponse;

            } else {
                logger.error("AI穿衣处理失败（官方格式）: {}", response.getErrorMsg());

                FittingRoomResponse errorResponse = new FittingRoomResponse();
                errorResponse.setCode(String.valueOf(response.getCode()));
                errorResponse.setMsg(response.getErrorMsg());
                return errorResponse;
            }

        } catch (Exception e) {
            logger.error("AI穿衣处理异常（官方格式）", e);
            throw e;
        }
    }

    /**
     * 生成并打印等效的curl命令
     *
     * @param apiPath API路径
     * @param bizContent 业务参数JSON
     * @param appToken 应用令牌
     */
    private void printCurlCommand(String apiPath, String bizContent, String appToken) {
        try {
            String baseUrl = apiConfig.getBaseUrl();
            String fullUrl = baseUrl + apiPath;

            // 构建curl命令
            StringBuilder curlCommand = new StringBuilder();
            curlCommand.append("curl -X POST \"").append(fullUrl).append("\" \\\n");
            curlCommand.append("  -H \"Content-Type: application/json\" \\\n");
            curlCommand.append("  -H \"Authorization: Bearer ").append(appToken).append("\" \\\n");
            curlCommand.append("  -d '").append(bizContent).append("'");

            logger.info("等效的官方API调用curl命令:\n{}", curlCommand.toString());

            // 也打印简化版本（不包含令牌）
            StringBuilder simpleCurl = new StringBuilder();
            simpleCurl.append("curl -X POST \"").append(fullUrl).append("\" \\\n");
            simpleCurl.append("  -H \"Content-Type: application/json\" \\\n");
            simpleCurl.append("  -H \"Authorization: Bearer [YOUR_TOKEN]\" \\\n");
            simpleCurl.append("  -d '").append(bizContent).append("'");

            logger.info("简化版curl命令（隐藏令牌）:\n{}", simpleCurl.toString());

        } catch (Exception e) {
            logger.warn("生成curl命令失败", e);
        }
    }

    /**
     * 检查是否是令牌过期错误
     *
     * @param response API响应
     * @return 是否是令牌过期错误
     */
    private boolean isTokenExpiredError(CommonResponse response) {
        if (response == null) {
            return false;
        }

        // 检查错误码
        String code = String.valueOf(response.getCode());
        if ("20001".equals(code)) {
            return true;
        }

        // 检查错误消息
        String errorMsg = response.getErrorMsg();
        if (errorMsg != null) {
            String lowerMsg = errorMsg.toLowerCase();
            return lowerMsg.contains("授权权限不足") ||
                   lowerMsg.contains("token") && (lowerMsg.contains("expired") || lowerMsg.contains("invalid")) ||
                   lowerMsg.contains("unauthorized") ||
                   lowerMsg.contains("authentication failed");
        }

        return false;
    }

    /**
     * 商品替换
     *
     * @param request 商品替换请求参数
     * @return 商品替换结果
     * @throws Exception API调用异常
     */
    public ShopReplaceResponse processShopReplace(ShopReplaceRequest request) throws Exception {
        logger.info("开始处理商品替换请求: {}", request);

        // 获取应用令牌
        String appToken = tokenService.getAppToken();

        // 创建API请求 - 使用官方文档中的正确路径
        String apiPath = "/linkfox-ai/image/v2/make/shopReplace";
        CommonRequest apiRequest = new CommonRequest(apiPath, RequestMethod.POST_JSON);

        // 直接使用官方格式的参数
        String bizContent = JSON.toJSONString(request);
        apiRequest.setBizContent(bizContent);

        // 打印等效的curl命令
        printCurlCommand(apiPath, bizContent, appToken);

        try {
            // 使用executeAppToken方法，传入应用令牌
            CommonResponse response = getOpenClient().executeAppToken(apiRequest, appToken);

            // 检查是否是令牌过期错误，如果是则重试一次
            if (!response.isSuccess() && isTokenExpiredError(response)) {
                logger.warn("检测到令牌过期错误，尝试刷新令牌并重试: {}", response.getErrorMsg());

                // 强制刷新令牌
                String newToken = tokenService.forceRefreshToken();
                logger.info("令牌已刷新，使用新令牌重试API调用");

                // 更新curl命令中的令牌并重新打印
                printCurlCommand(apiPath, bizContent, newToken);

                // 使用新令牌重试
                response = getOpenClient().executeAppToken(apiRequest, newToken);
            }

            if (response.isSuccess()) {
                String data = response.getData();
                logger.info("商品替换处理成功: {}", data);

                // 创建成功响应
                ShopReplaceResponse shopReplaceResponse = new ShopReplaceResponse();
                shopReplaceResponse.setCode("0");  // 网关返回码，0表示成功
                shopReplaceResponse.setMsg("成功");
                shopReplaceResponse.setSuccess(true);  // 设置成功标志

                // 解析API响应数据
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = JSON.parseObject(data, Map.class);
                if (dataMap != null) {
                    ShopReplaceResponse.ResponseData responseData = new ShopReplaceResponse.ResponseData();

                    // 设置响应数据
                    Object msgObj = dataMap.get("msg");
                    if (msgObj != null) {
                        responseData.setMsg(msgObj.toString());
                    }

                    Object codeObj = dataMap.get("code");
                    if (codeObj != null) {
                        responseData.setCode(Integer.valueOf(codeObj.toString()));
                    }

                    Object traceIdObj = dataMap.get("traceId");
                    if (traceIdObj != null) {
                        responseData.setTraceId(traceIdObj.toString());
                    }

                    // 解析内部数据
                    @SuppressWarnings("unchecked")
                    Map<String, Object> innerData = (Map<String, Object>) dataMap.get("data");
                    if (innerData != null) {
                        ShopReplaceResponse.TaskData taskData = new ShopReplaceResponse.TaskData();
                        Object id = innerData.get("id");
                        if (id != null) {
                            taskData.setId(id.toString());
                        }
                        responseData.setData(taskData);
                    }

                    shopReplaceResponse.setData(responseData);
                }

                return shopReplaceResponse;

            } else {
                logger.error("商品替换处理失败: {}", response.getErrorMsg());

                ShopReplaceResponse errorResponse = new ShopReplaceResponse();
                errorResponse.setCode(String.valueOf(response.getCode()));
                errorResponse.setMsg(response.getErrorMsg());
                return errorResponse;
            }

        } catch (Exception e) {
            logger.error("商品替换处理异常", e);
            throw e;
        }
    }
}
